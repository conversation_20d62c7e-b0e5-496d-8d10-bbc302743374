2025-06-28 23:12:09,726 - INFO - SPY.py:2212 - Logging configured for Option Trader v3.1.
2025-06-28 23:12:09,726 - INFO - SPY.py:2213 - Log file: c:\Users\<USER>\Desktop\Spy option\rl_logs_yfinance_options_v3.1\spy_option_training_v3.1.log
2025-06-28 23:12:09,726 - INFO - SPY.py:2214 - Tickers to fetch: ['SPY', '^VIX', '^VIX3M', '^IRX', '^TNX', 'IMPGSC1']
2025-06-28 23:12:09,737 - INFO - SPY.py:2243 - Initialized shared yfinance session with browser-like headers and very conservative rate limiting.
2025-06-28 23:12:09,739 - INFO - SPY.py:6161 - --- Option Trader v3.1 ---
2025-06-28 23:12:09,739 - INFO - SPY.py:6266 - [INFO] Main: Preparing Data for Training
2025-06-28 23:12:09,739 - INFO - SPY.py:6279 - Fetching train data for SPY (elapsed: 0.0s)
2025-06-28 23:12:09,739 - INFO - SPY.py:9385 - [INFO] Main: Script execution completed
2025-06-28 23:12:09,739 - INFO - SPY.py:800 - [INFO] PerformanceMonitor: Performance Summary: 0/0 operations successful (0.0% success rate)
